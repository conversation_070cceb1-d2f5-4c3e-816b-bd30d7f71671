# Analysis Worker Service

🧠 **AI-Powered Persona Analysis Microservice** for the ATMA Backend Platform

## Overview

The Analysis Worker is a stateless background service that performs intensive AI-powered personality analysis using Google Gemini. It processes jobs from RabbitMQ queues, analyzes psychometric test data, and generates comprehensive persona profiles.

## 🎯 Core Responsibilities

- **Queue Processing**: Continuously monitors and processes jobs from RabbitMQ
- **Data Retrieval**: Fetches raw psychometric test data from PostgreSQL database
- **AI Analysis**: Uses Google Gemini API with structured output for persona analysis
- **Result Storage**: Updates Archive Service with analysis results
- **Event Notification**: Publishes completion/failure events to notification queues
- **Error Handling**: Robust error handling with retry mechanisms

## 🛠 Technology Stack

- **Runtime**: Node.js 20+ with ES Modules
- **Queue**: RabbitMQ (amqplib)
- **Database**: PostgreSQL (pg)
- **AI Service**: Google Gemini API (@google/genai)
- **HTTP Client**: Axios
- **Environment**: dotenv

## 📁 Project Structure

```
analysis-worker/
├── src/
│   ├── services/
│   │   ├── geminiService.js      # Google Gemini AI integration
│   │   └── archiveClient.js      # Archive Service HTTP client
│   ├── database/
│   │   └── profileRepository.js  # Database operations
│   ├── worker.js                 # Core worker logic
│   └── index.js                  # Application entry point
├── .env.example                  # Environment variables template
├── Dockerfile                    # Container configuration
├── .dockerignore                 # Docker build exclusions
└── package.json                  # Dependencies and scripts
```

## 🚀 Quick Start

### Prerequisites

- Node.js 20+
- PostgreSQL database with `archive_schema.persona_profiles` table
- RabbitMQ server
- Google Gemini API key

### Installation

1. **Clone and install dependencies**:
   ```bash
   npm install
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the service**:
   ```bash
   npm start
   ```

### Development Mode

```bash
npm run dev  # Runs with --watch flag for auto-restart
```

## ⚙️ Configuration

### Required Environment Variables

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_db
DB_USER=analysis_worker
DB_PASSWORD=your_password

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
ANALYSIS_JOBS_QUEUE=analysis_jobs_queue
NOTIFICATION_EVENTS_QUEUE=notification_events

# Google Gemini AI Configuration
GEMINI_API_KEY=your_gemini_api_key
GEMINI_MODEL=gemini-1.5-flash

# Archive Service Configuration
ARCHIVE_SERVICE_URL=http://localhost:3001

# Application Configuration
NODE_ENV=production
LOG_LEVEL=info
```

## 📊 Job Processing Flow

1. **Job Reception**: Receives job from `analysis_jobs_queue`
   ```json
   { "profileId": "uuid", "userId": "uuid" }
   ```

2. **Status Update**: Marks profile as "processing" in Archive Service

3. **Data Retrieval**: Fetches `raw_input_data` from database using `profileId`

4. **AI Analysis**: Sends structured prompt to Gemini API with RIASEC and OCEAN data

5. **Result Processing**: Receives structured JSON response with:
   - Personality summary
   - Top 3 traits
   - Career recommendations (5-8 options)
   - Strengths analysis
   - Development areas

6. **Result Storage**: Updates Archive Service with analysis results

7. **Notification**: Publishes completion event to notification queue

8. **Acknowledgment**: Confirms job completion to RabbitMQ

## 🏗️ Service Architecture & Interactions

### Service Flow Overview
```
API Gateway (Receptionist) → Assessment Service (Waiter) → Analysis Worker (Koki)
```

The Analysis Worker operates as the **"Koki" (Chef)** in the ATMA microservices ecosystem:
- **Assessment Service** acts as the **"Waiter"** - receives orders from users and delegates work
- **Analysis Worker** acts as the **"Koki"** - performs the intensive AI analysis cooking
- **Archive Service** acts as the **"Storage"** - stores and manages all profile data

### 🔄 Complete Service Interaction Flow

#### 1. Assessment Service → Analysis Worker
**What Assessment Service Should Do:**

1. **Create Profile Record**: First create a profile record in Archive Service with status `pending`
   ```json
   POST /profiles
   {
     "userId": "user-uuid",
     "raw_input_data": {
       "riasec": { "realistic": 85, "investigative": 92, ... },
       "ocean": { "openness": 78, "conscientiousness": 88, ... }
     },
     "status": "pending"
   }
   ```

2. **Queue Analysis Job**: Send job to RabbitMQ `analysis_jobs_queue`
   ```json
   {
     "profileId": "profile-uuid",
     "userId": "user-uuid"
   }
   ```

3. **Return Immediate Response**: Return profile ID to user for tracking
   ```json
   {
     "profileId": "profile-uuid",
     "status": "pending",
     "message": "Analysis queued successfully"
   }
   ```

#### 2. Analysis Worker Processing
**What Analysis Worker Does Automatically:**

1. **Receives Job**: Listens on `analysis_jobs_queue` for new jobs
2. **Updates Status**: Marks profile as `processing` in Archive Service
3. **Fetches Data**: Retrieves `raw_input_data` from database
4. **AI Analysis**: Processes data through Google Gemini API
5. **Stores Results**: Updates Archive Service with analysis results
6. **Sends Notification**: Publishes completion event to `notification_events` queue
7. **Acknowledges Job**: Confirms job completion to RabbitMQ

#### 3. Archive Service → Analysis Worker
**What Archive Service Should Provide:**

**Database Schema Requirements:**
```sql
CREATE TABLE archive_schema.persona_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  raw_input_data JSONB NOT NULL,
  status VARCHAR(20) DEFAULT 'pending',
  persona_result JSONB,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  completed_at TIMESTAMP
);
```

**API Endpoints for Analysis Worker:**
- `PATCH /internal/profiles/:profileId` - Update profile status and results
- `GET /internal/profiles/:profileId` - Fetch profile data (if needed)

### 📋 Data Contracts

#### Input Data Format (from Assessment Service)
```json
{
  "riasec": {
    "realistic": 85,
    "investigative": 92,
    "artistic": 45,
    "social": 78,
    "enterprising": 67,
    "conventional": 55
  },
  "ocean": {
    "openness": 78,
    "conscientiousness": 88,
    "extraversion": 65,
    "agreeableness": 82,
    "neuroticism": 35
  }
}
```

#### Output Data Format (to Archive Service)
```json
{
  "personality_summary": {
    "title": "Creative Innovator",
    "summary_text": "A highly analytical and creative individual..."
  },
  "top_3_traits": ["Creative", "Analytical", "Empathetic"],
  "career_recommendations": [
    {
      "career": "UX Designer",
      "match_score": 92,
      "reason": "Strong creative and analytical skills align perfectly..."
    }
  ],
  "strengths_analysis": "Your analytical mindset combined with creative thinking...",
  "development_areas": "Consider developing stronger interpersonal skills..."
}
```

#### Notification Events Format
```json
{
  "userId": "user-uuid",
  "event": "ANALYSIS_COMPLETE", // or "ANALYSIS_FAILED"
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "profileId": "profile-uuid",
    "success": true,
    "error": "error message if failed"
  }
}
```

## 🔧 API Integration

### Archive Service Endpoints

- `PATCH /internal/profiles/:profileId` - Update profile status and results

### 🎯 What Analysis Worker Expects

#### From Assessment Service:
1. **Valid Job Messages**: Properly formatted JSON with `profileId` and `userId`
2. **Profile Pre-creation**: Profile must exist in Archive Service before queuing job
3. **Complete Raw Data**: Both RIASEC and OCEAN scores must be present in `raw_input_data`
4. **Proper Queue Setup**: RabbitMQ queues must be declared and accessible

#### From Archive Service:
1. **Database Access**: Read access to `archive_schema.persona_profiles` table
2. **API Endpoints**: `/internal/profiles/:profileId` for status updates
3. **Data Persistence**: Reliable storage of analysis results
4. **Status Management**: Proper handling of status transitions

### 🚀 What Analysis Worker Provides

#### To Archive Service:
1. **Status Updates**: Real-time profile status updates (`processing`, `completed`, `failed`)
2. **Analysis Results**: Comprehensive AI-generated persona analysis
3. **Error Handling**: Detailed error messages for failed analyses
4. **Retry Logic**: Automatic retry for transient failures

#### To Notification System:
1. **Completion Events**: Success notifications with profile details
2. **Failure Events**: Error notifications with failure reasons
3. **Real-time Updates**: Immediate event publishing upon job completion

#### To System Monitoring:
1. **Processing Metrics**: Jobs processed, success/failure counts, success rates
2. **Performance Data**: Processing times and throughput statistics
3. **Health Status**: Connection status for all dependent services
4. **Error Logging**: Comprehensive error tracking and reporting

### 🔄 Error Handling & Recovery

#### Analysis Worker Handles:
- **Transient Failures**: Automatic retry with exponential backoff
- **Invalid Data**: Graceful error handling with detailed logging
- **Service Outages**: Queue message persistence and retry logic
- **Resource Limits**: Proper memory and processing management

#### Assessment Service Should Handle:
- **Queue Failures**: Implement retry logic for job submission
- **Profile Creation**: Ensure profile exists before queuing analysis
- **User Feedback**: Provide status updates to users during processing

#### Archive Service Should Handle:
- **Concurrent Updates**: Handle multiple status update requests
- **Data Validation**: Validate analysis results before storage
- **Backup & Recovery**: Ensure data persistence and recovery capabilities

### Database Schema

```sql
-- Expected table structure
archive_schema.persona_profiles (
  id UUID PRIMARY KEY,
  raw_input_data JSONB,  -- Contains riasec and ocean scores
  status VARCHAR,
  persona_result JSONB,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
```

### Gemini API Response Schema

```json
{
  "personality_summary": {
    "title": "Creative Innovator",
    "summary_text": "Comprehensive personality description..."
  },
  "top_3_traits": ["Creative", "Analytical", "Empathetic"],
  "career_recommendations": [
    {
      "career": "UX Designer",
      "match_score": 92,
      "reason": "Strong creative and analytical skills..."
    }
  ],
  "strengths_analysis": "Detailed strengths analysis...",
  "development_areas": "Areas for growth and development..."
}
```

### 🔗 Integration Checklist

#### For Assessment Service Developers:
- [ ] Create profile in Archive Service before queuing analysis job
- [ ] Include both `profileId` and `userId` in job message
- [ ] Ensure `raw_input_data` contains complete RIASEC and OCEAN scores
- [ ] Implement proper error handling for queue operations
- [ ] Set up notification event consumption for user updates
- [ ] Validate data format before sending to analysis queue

#### For Archive Service Developers:
- [ ] Implement `/internal/profiles/:profileId` PATCH endpoint
- [ ] Support status transitions: `pending` → `processing` → `completed`/`failed`
- [ ] Store `persona_result` as JSONB in database
- [ ] Handle concurrent status updates gracefully
- [ ] Provide database access for Analysis Worker
- [ ] Implement proper error responses for API calls

#### For System Administrators:
- [ ] Configure RabbitMQ with required queues (`analysis_jobs_queue`, `notification_events`)
- [ ] Set up PostgreSQL database with proper schema and permissions
- [ ] Configure Google Gemini API access and rate limits
- [ ] Monitor queue depths and processing times
- [ ] Set up log aggregation for all services
- [ ] Configure health checks and alerting

### 📊 Monitoring & Observability

#### Key Metrics to Track:
- **Queue Depth**: Number of pending jobs in `analysis_jobs_queue`
- **Processing Time**: Average time per analysis job
- **Success Rate**: Percentage of successfully completed analyses
- **Error Rate**: Failed jobs and error categories
- **API Response Times**: Archive Service and Gemini API latencies
- **Resource Usage**: CPU, memory, and network utilization

#### Recommended Alerts:
- Queue depth exceeding threshold (>100 jobs)
- Success rate dropping below 95%
- Processing time exceeding 60 seconds
- Archive Service API errors
- Database connection failures
- Gemini API rate limit hits

## 🐳 Docker Deployment

### Build Image

```bash
docker build -t analysis-worker:latest .
```

### Run Container

```bash
docker run -d \
  --name analysis-worker \
  --env-file .env \
  --restart unless-stopped \
  analysis-worker:latest
```

### Docker Compose Example

```yaml
version: '3.8'
services:
  analysis-worker:
    build: .
    environment:
      - DB_HOST=postgres
      - RABBITMQ_URL=amqp://rabbitmq:5672
      - ARCHIVE_SERVICE_URL=http://archive-service:3001
    depends_on:
      - postgres
      - rabbitmq
    restart: unless-stopped
```

## 📈 Monitoring & Logging

### Worker Statistics

The service logs processing statistics every minute:
- Jobs processed
- Success/failure counts
- Success rate percentage

### Health Checks

- Database connection validation on startup
- RabbitMQ connection monitoring
- Graceful shutdown on SIGINT/SIGTERM

### Error Handling

- Automatic retry for Archive Service calls
- Comprehensive error logging
- Failed job marking with error details
- Queue message acknowledgment regardless of outcome

## 🔒 Security Features

- Non-root Docker user
- Environment variable validation
- Input data sanitization
- Secure API key handling
- Connection timeout configurations

## 🧪 Testing

The service includes built-in test functions:
- Database connection testing
- Gemini API service testing
- Archive Service connectivity testing

## � Troubleshooting

### Common Integration Issues

#### 1. Jobs Not Being Processed
**Symptoms**: Jobs queued but Analysis Worker shows no activity
**Possible Causes**:
- RabbitMQ connection issues
- Queue name mismatch between services
- Analysis Worker not running or crashed

**Solutions**:
```bash
# Check RabbitMQ queue status
rabbitmqctl list_queues name messages

# Verify Analysis Worker logs
docker logs analysis-worker

# Check queue configuration
echo $ANALYSIS_JOBS_QUEUE  # Should match between services
```

#### 2. Profile Not Found Errors
**Symptoms**: "No raw data found for profile" errors
**Possible Causes**:
- Profile not created in Archive Service before queuing job
- Database connection issues
- Incorrect profile ID in job message

**Solutions**:
```bash
# Verify profile exists in database
psql -d atma_db -c "SELECT id, status FROM archive_schema.persona_profiles WHERE id = 'profile-uuid';"

# Check Assessment Service profile creation logic
# Ensure profile is created before job is queued
```

#### 3. Archive Service Update Failures
**Symptoms**: "Failed to update profile status" errors
**Possible Causes**:
- Archive Service not running or unreachable
- API endpoint not implemented
- Network connectivity issues

**Solutions**:
```bash
# Test Archive Service connectivity
curl -X PATCH http://archive-service:3001/internal/profiles/test-id \
  -H "Content-Type: application/json" \
  -d '{"status": "processing"}'

# Check Archive Service logs
docker logs archive-service

# Verify environment variable
echo $ARCHIVE_SERVICE_URL
```

#### 4. Gemini API Failures
**Symptoms**: "AI analysis failed" errors
**Possible Causes**:
- Invalid API key
- Rate limit exceeded
- Malformed input data

**Solutions**:
```bash
# Test Gemini API key
curl -H "Authorization: Bearer $GEMINI_API_KEY" \
  https://generativelanguage.googleapis.com/v1/models

# Check rate limits in Google Cloud Console
# Verify raw_input_data format contains both riasec and ocean
```

#### 5. Notification Events Not Sent
**Symptoms**: Users not receiving completion notifications
**Possible Causes**:
- Notification queue not configured
- Event consumer service not running
- Queue name mismatch

**Solutions**:
```bash
# Check notification queue
rabbitmqctl list_queues name messages | grep notification

# Verify queue name consistency
echo $NOTIFICATION_EVENTS_QUEUE

# Check notification service logs
```

### Performance Optimization

#### For High-Volume Processing:
1. **Scale Workers**: Run multiple Analysis Worker instances
2. **Optimize Database**: Add indexes on frequently queried columns
3. **Tune RabbitMQ**: Adjust prefetch count and queue durability
4. **Monitor Resources**: Track CPU, memory, and network usage
5. **Cache Results**: Consider caching for similar input patterns

#### Database Optimization:
```sql
-- Add indexes for better performance
CREATE INDEX idx_persona_profiles_status ON archive_schema.persona_profiles(status);
CREATE INDEX idx_persona_profiles_user_id ON archive_schema.persona_profiles(user_id);
CREATE INDEX idx_persona_profiles_created_at ON archive_schema.persona_profiles(created_at);
```

## �📝 Logging Format

```
🚀 Initializing Analysis Worker...
✅ Database connection successful
🔌 Connecting to RabbitMQ...
✅ Connected to RabbitMQ successfully
🎯 Starting Analysis Worker...
📥 Listening for jobs on queue: analysis_jobs_queue
🔄 Analysis Worker is now running and waiting for jobs...

🔥 Processing job for profile: abc-123, user: def-456
📊 Fetching raw input data...
🤖 Starting AI analysis...
📤 Archive Service Request: PATCH /internal/profiles/abc-123
✅ Job completed successfully in 3247ms
📈 Stats: Processed=1, Success=1, Failed=0
```

## 🤝 Contributing

1. Follow the existing code structure
2. Add comprehensive error handling
3. Include detailed logging
4. Test with sample data
5. Update documentation

## 📄 License

ISC License - ATMA Backend Team
