# Analysis Worker Service

🧠 **AI-Powered Persona Analysis Microservice** for the ATMA Backend Platform

## Overview

The Analysis Worker is a stateless background service that performs intensive AI-powered personality analysis using Google Gemini. It processes jobs from RabbitMQ queues, analyzes psychometric test data, and generates comprehensive persona profiles.

## 🎯 Core Responsibilities

- **Queue Processing**: Continuously monitors and processes jobs from RabbitMQ
- **Data Retrieval**: Fetches raw psychometric test data from PostgreSQL database
- **AI Analysis**: Uses Google Gemini API with structured output for persona analysis
- **Result Storage**: Updates Archive Service with analysis results
- **Event Notification**: Publishes completion/failure events to notification queues
- **Error Handling**: Robust error handling with retry mechanisms

## 🛠 Technology Stack

- **Runtime**: Node.js 20+ with ES Modules
- **Queue**: RabbitMQ (amqplib)
- **Database**: PostgreSQL (pg)
- **AI Service**: Google Gemini API (@google/genai)
- **HTTP Client**: Axios
- **Environment**: dotenv

## 📁 Project Structure

```
analysis-worker/
├── src/
│   ├── services/
│   │   ├── geminiService.js      # Google Gemini AI integration
│   │   └── archiveClient.js      # Archive Service HTTP client
│   ├── database/
│   │   └── profileRepository.js  # Database operations
│   ├── worker.js                 # Core worker logic
│   └── index.js                  # Application entry point
├── .env.example                  # Environment variables template
├── Dockerfile                    # Container configuration
├── .dockerignore                 # Docker build exclusions
└── package.json                  # Dependencies and scripts
```

## 🚀 Quick Start

### Prerequisites

- Node.js 20+
- PostgreSQL database with `archive_schema.persona_profiles` table
- RabbitMQ server
- Google Gemini API key

### Installation

1. **Clone and install dependencies**:
   ```bash
   npm install
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the service**:
   ```bash
   npm start
   ```

### Development Mode

```bash
npm run dev  # Runs with --watch flag for auto-restart
```

## ⚙️ Configuration

### Required Environment Variables

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_db
DB_USER=analysis_worker
DB_PASSWORD=your_password

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
ANALYSIS_JOBS_QUEUE=analysis_jobs_queue
NOTIFICATION_EVENTS_QUEUE=notification_events

# Google Gemini AI Configuration
GEMINI_API_KEY=your_gemini_api_key
GEMINI_MODEL=gemini-1.5-flash

# Archive Service Configuration
ARCHIVE_SERVICE_URL=http://localhost:3001

# Application Configuration
NODE_ENV=production
LOG_LEVEL=info
```

## 📊 Job Processing Flow

1. **Job Reception**: Receives job from `analysis_jobs_queue`
   ```json
   { "profileId": "uuid", "userId": "uuid" }
   ```

2. **Status Update**: Marks profile as "processing" in Archive Service

3. **Data Retrieval**: Fetches `raw_input_data` from database using `profileId`

4. **AI Analysis**: Sends structured prompt to Gemini API with RIASEC and OCEAN data

5. **Result Processing**: Receives structured JSON response with:
   - Personality summary
   - Top 3 traits
   - Career recommendations (5-8 options)
   - Strengths analysis
   - Development areas

6. **Result Storage**: Updates Archive Service with analysis results

7. **Notification**: Publishes completion event to notification queue

8. **Acknowledgment**: Confirms job completion to RabbitMQ

## 🔧 API Integration

### Archive Service Endpoints

- `PATCH /internal/profiles/:profileId` - Update profile status and results

### Database Schema

```sql
-- Expected table structure
archive_schema.persona_profiles (
  id UUID PRIMARY KEY,
  raw_input_data JSONB,  -- Contains riasec and ocean scores
  status VARCHAR,
  persona_result JSONB,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
```

### Gemini API Response Schema

```json
{
  "personality_summary": {
    "title": "Creative Innovator",
    "summary_text": "Comprehensive personality description..."
  },
  "top_3_traits": ["Creative", "Analytical", "Empathetic"],
  "career_recommendations": [
    {
      "career": "UX Designer",
      "match_score": 92,
      "reason": "Strong creative and analytical skills..."
    }
  ],
  "strengths_analysis": "Detailed strengths analysis...",
  "development_areas": "Areas for growth and development..."
}
```

## 🐳 Docker Deployment

### Build Image

```bash
docker build -t analysis-worker:latest .
```

### Run Container

```bash
docker run -d \
  --name analysis-worker \
  --env-file .env \
  --restart unless-stopped \
  analysis-worker:latest
```

### Docker Compose Example

```yaml
version: '3.8'
services:
  analysis-worker:
    build: .
    environment:
      - DB_HOST=postgres
      - RABBITMQ_URL=amqp://rabbitmq:5672
      - ARCHIVE_SERVICE_URL=http://archive-service:3001
    depends_on:
      - postgres
      - rabbitmq
    restart: unless-stopped
```

## 📈 Monitoring & Logging

### Worker Statistics

The service logs processing statistics every minute:
- Jobs processed
- Success/failure counts
- Success rate percentage

### Health Checks

- Database connection validation on startup
- RabbitMQ connection monitoring
- Graceful shutdown on SIGINT/SIGTERM

### Error Handling

- Automatic retry for Archive Service calls
- Comprehensive error logging
- Failed job marking with error details
- Queue message acknowledgment regardless of outcome

## 🔒 Security Features

- Non-root Docker user
- Environment variable validation
- Input data sanitization
- Secure API key handling
- Connection timeout configurations

## 🧪 Testing

The service includes built-in test functions:
- Database connection testing
- Gemini API service testing
- Archive Service connectivity testing

## 📝 Logging Format

```
🚀 Initializing Analysis Worker...
✅ Database connection successful
🔌 Connecting to RabbitMQ...
✅ Connected to RabbitMQ successfully
🎯 Starting Analysis Worker...
📥 Listening for jobs on queue: analysis_jobs_queue
🔄 Analysis Worker is now running and waiting for jobs...

🔥 Processing job for profile: abc-123, user: def-456
📊 Fetching raw input data...
🤖 Starting AI analysis...
📤 Archive Service Request: PATCH /internal/profiles/abc-123
✅ Job completed successfully in 3247ms
📈 Stats: Processed=1, Success=1, Failed=0
```

## 🤝 Contributing

1. Follow the existing code structure
2. Add comprehensive error handling
3. Include detailed logging
4. Test with sample data
5. Update documentation

## 📄 License

ISC License - ATMA Backend Team
